# NOTE: this workflow is now running on Dronecode / PX4 AWS account.
# - If you want to keep the tests running in GitHub Actions you need to uncomment the "runs-on: ubuntu-latest" lines
#   and comment the "runs-on: [runs-on,runner=..." lines.
# - If you would like to duplicate this setup try setting up "RunsOn" on your own AWS account try https://runs-on.com

name: Build all targets

on:
  push:
    tags:
      - 'v*'
    branches:
      - 'main'
      - 'stable'
      - 'beta'
      - 'release/**'
    paths-ignore:
      - 'docs/**'
  pull_request:
    branches:
      - '*'
    paths-ignore:
      - 'docs/**'

concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

jobs:
  group_targets:
    name: Scan for Board Targets
    # runs-on: ubuntu-latest
    runs-on: [runs-on,runner=1cpu-linux-x64,image=ubuntu22-full-x64,"run-id=${{ github.run_id }}",spot=false]
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      timestamp: ${{ steps.set-timestamp.outputs.timestamp }}
      branchname: ${{ steps.set-branch.outputs.branchname }}
    steps:
      - uses: actions/checkout@v4

      - name: Update python packaging to avoid canonicalize_version() error
        run: |
            pip3 install -U packaging

      - name: Install Python Dependencies
        uses: py-actions/py-dependency-install@v4
        with:
          path: "./Tools/setup/requirements.txt"

      - id: set-matrix
        run: echo "::set-output name=matrix::$(./Tools/ci/generate_board_targets_json.py --group)"

      - id: set-timestamp
        run: echo "::set-output name=timestamp::$(date +"%Y%m%d%H%M%S")"

      - id: set-branch
        run: |
          echo "branchname=${{
            github.event_name == 'pull_request' &&
            format('pr-{0}', github.event.pull_request.number) ||
            github.head_ref ||
            github.ref_name
          }}" >> $GITHUB_OUTPUT

      - name: Debug Matrix Output
        if: runner.debug == '1'
        run: |
          echo "${{ steps.set-timestamp.outputs.timestamp }}"
          echo "${{ steps.set-branch.outputs.branchname }}"
          echo "$(./Tools/ci/generate_board_targets_json.py --group --verbose)"

  setup:
    name: Build Group [${{ matrix.group }}][${{ matrix.arch == 'nuttx' && 'x86' || 'arm64' }}]
    # runs-on: ubuntu-latest
    runs-on: [runs-on,"runner=8cpu-linux-${{ matrix.runner }}","image=ubuntu24-full-${{ matrix.runner }}","run-id=${{ github.run_id }}",spot=false]
    needs: group_targets
    strategy:
      matrix: ${{ fromJson(needs.group_targets.outputs.matrix) }}
      fail-fast: false
    container:
      image: ${{ matrix.container }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Git ownership workaround
        run: git config --system --add safe.directory '*'

      - name: Setup ccache
        uses: actions/cache@v4
        with:
          path: ~/.ccache
          key: ${{ matrix.group }}-ccache-${{ needs.group_targets.outputs.timestamp }}
          restore-keys: ${{ matrix.group }}-ccache-${{ needs.group_targets.outputs.timestamp }}

      - name: Configure ccache
        run: |
            mkdir -p ~/.ccache
            echo "base_dir = ${GITHUB_WORKSPACE}" > ~/.ccache/ccache.conf
            echo "compression = true" >> ~/.ccache/ccache.conf
            echo "compression_level = 6" >> ~/.ccache/ccache.conf
            echo "max_size = 120M" >> ~/.ccache/ccache.conf
            echo "hash_dir = false" >> ~/.ccache/ccache.conf
            ccache -s
            ccache -z

      - name: Building [${{ matrix.group }}]
        run: |
            ./Tools/ci/build_all_runner.sh ${{matrix.targets}} ${{matrix.arch}}

      - name: Arrange Build Artifacts
        run: |
          ./Tools/ci/package_build_artifacts.sh

      - name: Upload Build Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: px4_${{matrix.group}}_build_artifacts
          path: artifacts/

      - name: Cache Save
        run: ccache -s

  artifacts:
    name: Upload Artifacts to S3
    # runs-on: ubuntu-latest
    runs-on: [runs-on,runner=1cpu-linux-x64,image=ubuntu24-full-x64,"run-id=${{ github.run_id }}",spot=false]
    needs: [setup, group_targets]
    if: contains(fromJSON('["main", "stable", "beta"]'), needs.group_targets.outputs.branchname)
    steps:
      - name: Download Artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts/
          merge-multiple: true

      - name: Branch Name
        run: |
          echo "${{ needs.group_targets.outputs.branchname }}"

      - name: Uploading Artifacts to S3 [${{ needs.group_targets.outputs.branchname == 'main' && 'master' || needs.group_targets.outputs.branchname }}]
        uses: jakejarvis/s3-sync-action@master
        with:
          args: --acl public-read
        env:
          AWS_S3_BUCKET: 'px4-travis'
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_REGION: 'us-west-1'
          SOURCE_DIR: artifacts/
          DEST_DIR: Firmware/${{ needs.group_targets.outputs.branchname == 'main' && 'master' || needs.group_targets.outputs.branchname }}/

  release:
    name: Create Release and Upload Artifacts
    permissions:
      contents: write
    # runs-on: ubuntu-latest
    runs-on: [runs-on,runner=1cpu-linux-x64,image=ubuntu24-full-x64,"run-id=${{ github.run_id }}",spot=false]
    needs: [setup, group_targets]
    if: startsWith(github.ref, 'refs/tags/')
    steps:
      - name: Download Artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts/
          merge-multiple: true

      - name: Upload Binaries to Release
        uses: softprops/action-gh-release@v2
        with:
          draft: true
          files: artifacts/*.px4
