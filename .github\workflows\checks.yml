name: Checks

on:
  push:
    branches:
    - 'main'
    paths-ignore:
      - 'docs/**'
  pull_request:
    branches:
    - '*'
    paths-ignore:
      - 'docs/**'

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      fail-fast: false
      matrix:
        check: [
          "check_format",
          "check_newlines",
          "tests",
          "tests_coverage",
          "px4_fmu-v2_default stack_check",
          "validate_module_configs",
          "shellcheck_all",
          "NO_NINJA_BUILD=1 px4_fmu-v5_default",
          "NO_NINJA_BUILD=1 px4_sitl_default",
          "px4_sitl_allyes",
          "module_documentation",
        ]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Building [${{ matrix.check }}]
        uses: addnab/docker-run-action@v3
        with:
          image: px4io/px4-dev-nuttx-focal:2022-08-12
          options: -v ${{ github.workspace }}:/workspace
          run: |
            cd /workspace
            git config --global --add safe.directory /workspace
            make ${{ matrix.check }}

      - name: Uploading Coverage to Codecov.io
        if: contains(matrix.check, 'coverage')
        uses: codecov/codecov-action@v1
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          flags: unittests
          file: coverage/lcov.info
