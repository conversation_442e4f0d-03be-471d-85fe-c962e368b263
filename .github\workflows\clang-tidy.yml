name: Clang Tidy

on:
  push:
    branches:
    - 'main'
    paths-ignore:
      - 'docs/**'
  pull_request:
    branches:
    - '*'
    paths-ignore:
      - 'docs/**'
jobs:
  build:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Testing (clang-tidy-quiet)
      uses: addnab/docker-run-action@v3
      with:
        image: px4io/px4-dev-clang:2021-09-08
        options: -v ${{ github.workspace }}:/workspace
        run: |
          cd /workspace
          git config --global --add safe.directory /workspace
          make clang-tidy-quiet
