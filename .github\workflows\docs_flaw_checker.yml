name: Doc<PERSON> - Check for flaws in PX4 Guide Source
# So far:
  # Modifications of translations files
  # Broken internal links

on:
  pull_request_target:
    types: [opened, edited, synchronize]
    paths:
      - 'docs/en/**'

jobs:
  check_flaws:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Install Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18'

      - name: Create logs directory
        run: |
          mkdir logs

      - name: Get changed english doc files
        id: get_changed_markdown_english
        uses: tj-actions/changed-files@v35.9.2
        with:
          json: true
          base_sha: "${{ github.event.pull_request.base.sha }}"
          sha: "${{ github.event.pull_request.head.sha }}"
          # Below are used to output files to a directory. May use in flaw checker.
          # write_output_files: true
          # output_dir: "./logs"
          files: |
            docs/en/**/*.md

      - name: Save JSON file containing files to link check
        run: |
          echo "${{ steps.get_changed_markdown_english.outputs.all_changed_files }}"
          echo "${{ steps.get_changed_markdown_english.outputs.all_changed_files }}" > ./logs/prFiles.json

      - name: Run link checker
        id: link-check
        run: |
          npm -g install markdown_link_checker_sc@0.0.138
          markdown_link_checker_sc -r ${{ github.workspace }} -d docs -e en -f ./logs/prFiles.json -i assets -u docs.px4.io/main/ > ./logs/errorsFilteredByPrPages.md
          mkdir -p ./pr
          cp ./logs/errorsFilteredByPrPages.md ./pr/errorsFilteredByPrPages.md

      - name: Read errorsFilteredByPrPages.md file
        id: read-errors-by-page
        uses: juliangruber/read-file-action@v1
        with:
          path: ./logs/errorsFilteredByPrPages.md

      - name: Echo Errors by Page
        run: echo "${{ steps.read-errors-by-page.outputs.content }}"

      - name: Save PR number
        env:
          PR_NUMBER: ${{ github.event.number }}
        run: |
          #mkdir -p ./pr
          echo $PR_NUMBER > ./pr/pr_number
      - uses: actions/upload-artifact@v4
        with:
          name: pr_number
          path: pr/
