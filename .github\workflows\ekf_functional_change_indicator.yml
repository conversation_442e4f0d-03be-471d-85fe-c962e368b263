name: EKF Change Indicator

on:
  pull_request:
    branches:
    - '*'
    paths-ignore:
      - 'docs/**'

jobs:
  unit_tests:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: main test
      uses: addnab/docker-run-action@v3
      with:
        image: px4io/px4-dev-base-focal:2021-09-08
        options: -v ${{ github.workspace }}:/workspace
        run: |
          cd /workspace
          git config --global --add safe.directory /workspace
          make tests TESTFILTER=EKF

    - name: Check if there is a functional change
      run: git diff --exit-code
      working-directory: src/modules/ekf2/test/change_indication
