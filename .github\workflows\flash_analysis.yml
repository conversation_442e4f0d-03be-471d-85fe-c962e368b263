name: FLASH usage analysis

permissions:
  contents: read
  pull-requests: write
  issues: write

on:
  push:
    branches:
      - 'main'
    paths-ignore:
      - 'docs/**'
  pull_request:
    branches:
      - '*'
    paths-ignore:
      - 'docs/**'

env:
  MIN_FLASH_POS_DIFF_FOR_COMMENT: 50
  MIN_FLASH_NEG_DIFF_FOR_COMMENT: -50

jobs:
  analyze_flash:
    name: Analyzing ${{ matrix.target }}
    runs-on: [runs-on,runner=8cpu-linux-x64,image=ubuntu24-full-x64,"run-id=${{ github.run_id }}",spot=false]
    container:
      image: px4io/px4-dev:v1.16.0-rc1-258-g0369abd556
    strategy:
      matrix:
        target: [px4_fmu-v5x, px4_fmu-v6x]
    outputs:
      px4_fmu-v5x-bloaty-output: ${{ steps.gen-output.outputs.px4_fmu-v5x-bloaty-output }}
      px4_fmu-v5x-bloaty-summary-map: ${{ steps.gen-output.outputs.px4_fmu-v5x-bloaty-summary-map }}
      px4_fmu-v6x-bloaty-output: ${{ steps.gen-output.outputs.px4_fmu-v6x-bloaty-output }}
      px4_fmu-v6x-bloaty-summary-map: ${{ steps.gen-output.outputs.px4_fmu-v6x-bloaty-summary-map }}
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: recursive

      - name: Git ownership workaround
        run: git config --system --add safe.directory '*'

      - name: Build Target
        run: make ${{ matrix.target }}_flash-analysis

      - name: Store the ELF with the change
        run: cp ./build/**/*.elf ./with-change.elf

      - name: Clean previous build
        run: |
          make clean
          make distclean

      - name: If it's a PR checkout the base branch
        if: ${{ github.event.pull_request }}
        # As checkout creates a merge commit (merging the base branch into the PR branch), the base branch is the base for a diff of the PR changes.
        run: git checkout ${{ github.event.pull_request.base.ref }}

      - name: If it's a push checkout the previous commit
        if: github.event_name == 'push'
        run: git checkout ${{ github.event.before }}

      - name: Update submodules
        run: make submodulesupdate

      - name: Build
        run: make ${{ matrix.target }}_flash-analysis

      - name: Store the ELF before the change
        run: cp ./build/**/*.elf ./before-change.elf

      - name: bloaty-action
        uses: PX4/bloaty-action@v1.0.0
        id: bloaty-step
        with:
          bloaty-file-args: ./with-change.elf -- ./before-change.elf
          bloaty-additional-args: -d sections,symbols -s vm -n 20
          output-to-summary: true

      - name: Generate output
        id: gen-output
        run: |
          EOF=$(dd if=/dev/urandom bs=15 count=1 status=none | base64)
          echo "${{ matrix.target }}-bloaty-output<<$EOF" >> $GITHUB_OUTPUT
          echo "${{ steps.bloaty-step.outputs.bloaty-output-encoded }}" >> $GITHUB_OUTPUT
          echo "$EOF" >> $GITHUB_OUTPUT
          echo "${{ matrix.target }}-bloaty-summary-map<<$EOF" >> $GITHUB_OUTPUT
          echo '${{ steps.bloaty-step.outputs.bloaty-summary-map }}' >> $GITHUB_OUTPUT
          echo "$EOF" >> $GITHUB_OUTPUT

  # TODO:
  # This part of the workflow is causing errors for forks. We should find a way to fix this and enable it again for forks.
  # Track this issue https://github.com/PX4/PX4-Autopilot/issues/24408
  post_pr_comment:
    name: Publish Results
    runs-on: [runs-on,runner=1cpu-linux-x64,image=ubuntu24-full-x64,"run-id=${{ github.run_id }}",spot=false]
    needs: [analyze_flash]
    env:
      V5X-SUMMARY-MAP-ABS: ${{ fromJSON(fromJSON(needs.analyze_flash.outputs.px4_fmu-v5x-bloaty-summary-map).vm-absolute) }}
      V5X-SUMMARY-MAP-PERC: ${{ fromJSON(fromJSON(needs.analyze_flash.outputs.px4_fmu-v5x-bloaty-summary-map).vm-percentage) }}
      V6X-SUMMARY-MAP-ABS: ${{ fromJSON(fromJSON(needs.analyze_flash.outputs.px4_fmu-v6x-bloaty-summary-map).vm-absolute) }}
      V6X-SUMMARY-MAP-PERC: ${{ fromJSON(fromJSON(needs.analyze_flash.outputs.px4_fmu-v6x-bloaty-summary-map).vm-percentage) }}
    if: github.event.pull_request && github.event.pull_request.head.repo.full_name == github.repository
    steps:
      - name: Find Comment
        uses: peter-evans/find-comment@v3
        id: fc
        with:
          issue-number: ${{ github.event.pull_request.number }}
          comment-author: 'github-actions[bot]'
          body-includes: FLASH Analysis

      - name: Set Build Time
        id: bt
        run: |
          echo "timestamp=$(date +'%Y-%m-%dT%H:%M:%S')" >> $GITHUB_OUTPUT

      - name: Create or update comment
        # This can't be moved to the job-level conditions, as GH actions don't allow a job-level if condition to access the env.
        if: |
          steps.fc.outputs.comment-id != '' ||
          env.V5X-SUMMARY-MAP-ABS >= fromJSON(env.MIN_FLASH_POS_DIFF_FOR_COMMENT) ||
          env.V5X-SUMMARY-MAP-ABS <= fromJSON(env.MIN_FLASH_NEG_DIFF_FOR_COMMENT) ||
          env.V6X-SUMMARY-MAP-ABS >= fromJSON(env.MIN_FLASH_POS_DIFF_FOR_COMMENT) ||
          env.V6X-SUMMARY-MAP-ABS <= fromJSON(env.MIN_FLASH_NEG_DIFF_FOR_COMMENT)
        uses: peter-evans/create-or-update-comment@v4
        with:
          comment-id: ${{ steps.fc.outputs.comment-id }}
          issue-number: ${{ github.event.pull_request.number }}
          body: |
            ## 🔎 FLASH Analysis
            <details>
              <summary>px4_fmu-v5x [Total VM Diff: ${{ env.V5X-SUMMARY-MAP-ABS }} byte (${{ env.V5X-SUMMARY-MAP-PERC}} %)]</summary>

              ```
              ${{ needs.analyze_flash.outputs.px4_fmu-v5x-bloaty-output }}
              ```
            </details>

            <details>
              <summary>px4_fmu-v6x [Total VM Diff: ${{ env.V6X-SUMMARY-MAP-ABS }} byte (${{ env.V6X-SUMMARY-MAP-PERC }} %)]</summary>

              ```
              ${{ needs.analyze_flash.outputs.px4_fmu-v6x-bloaty-output }}
              ```
            </details>

            **Updated: _${{ steps.bt.outputs.timestamp }}_**
          edit-mode: replace
