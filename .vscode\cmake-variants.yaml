CONFIG:
  default: px4_sitl_default
  choices:
    px4_sitl_default:
      short: px4_sitl_default
      buildType: RelWithDebInfo
      settings:
        CONFIG: px4_sitl_default
    px4_sitl_spacecraft:
      short: px4_sitl_spacecraft
      buildType: RelWithDebInfo
      settings:
        CONFIG: px4_sitl_spacecraft
    px4_sitl_nolockstep:
      short: px4_sitl_nolockstep
      buildType: RelWithDebInfo
      settings:
        CONFIG: px4_sitl_nolockstep
    px4_sitl_asan:
      short: px4_sitl (AddressSanitizer)
      buildType: AddressSanitizer
      settings:
        CONFIG: px4_sitl_default
    px4_sitl_ubsan:
      short: px4_sitl (UndefinedBehaviorSanitizer)
      buildType: UndefinedBehaviorSanitizer
      settings:
        CONFIG: px4_sitl_default
    px4_sitl_replay:
      short: px4_sitl_replay
      buildType: RelWithDebInfo
      settings:
        CONFIG: px4_sitl_replay
    px4_sitl_test:
      short: px4_sitl_test
      buildType: RelWithDebInfo
      settings:
        CONFIG: px4_sitl_test
    px4_io-v2_default:
      short: px4_io-v2
      buildType: MinSizeRel
      settings:
        CONFIG: px4_io-v2_default
    px4_fmu-v2_default:
      short: px4_fmu-v2
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v2_default
    px4_fmu-v3_default:
      short: px4_fmu-v3
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v3_default
    px4_fmu-v4_default:
      short: px4_fmu-v4
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v4_default
    px4_fmu-v4pro_default:
      short: px4_fmu-v4pro
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v4pro_default
    px4_fmu-v5_default:
      short: px4_fmu-v5
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v5_default
    px4_fmu-v5_debug:
      short: px4_fmu-v5_debug
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v5_debug
    px4_fmu-v5x_default:
      short: px4_fmu-v5x
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v5x_default
    px4_fmu-v6c_default:
      short: px4_fmu-v6c
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v6c_default
    px4_fmu-v6c_bootloader:
      short: px4_fmu-v6c_bootloader
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v6c_bootloader
    px4_fmu-v6u_default:
      short: px4_fmu-v6u
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v6u_default
    px4_fmu-v6u_bootloader:
      short: px4_fmu-v6u_bootloader
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v6u_bootloader
    px4_fmu-v6x_default:
      short: px4_fmu-v6x
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v6x_default
    px4_fmu-v6x_bootloader:
      short: px4_fmu-v6x_bootloader
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v6x_bootloader
    px4_fmu-v6xrt_default:
      short: px4_fmu-v6xrt
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v6xrt_default
    px4_fmu-v6xrt_bootloader:
      short: px4_fmu-v6xrt_bootloader
      buildType: MinSizeRel
      settings:
        CONFIG: px4_fmu-v6xrt_bootloader
    3dr_ctrl-zero-h7-oem-revg_default:
      short: 3dr_ctrl-zero-h7-oem-revg
      buildType: MinSizeRel
      settings:
        CONFIG: 3dr_ctrl-zero-h7-oem-revg_default
    airmind_mindpx-v2_default:
      short: airmind_mindpx-v2
      buildType: MinSizeRel
      settings:
        CONFIG: airmind_mindpx-v2_default
    ark_can-flow_default:
      short: ark_can-flow_default
      buildType: MinSizeRel
      settings:
        CONFIG: ark_can-flow_default
    ark_can-flow_canbootloader:
      short: ark_can-flow_canbootloader
      buildType: MinSizeRel
      settings:
        CONFIG: ark_can-flow_canbootloader
    ark_can-flow-mr_canbootloader:
      short: ark_can-flow-mr_canbootloader
      buildType: MinSizeRel
      settings:
        CONFIG: ark_can-flow-mr_canbootloader
    ark_can-gps_default:
      short: ark_can-gps_default
      buildType: MinSizeRel
      settings:
        CONFIG: ark_can-gps_default
    ark_can-gps_canbootloader:
      short: ark_can-gps_canbootloader
      buildType: MinSizeRel
      settings:
        CONFIG: ark_can-gps_canbootloader
    ark_can-rtk-gps_default:
      short: ark_can-rtk-gps_default
      buildType: MinSizeRel
      settings:
        CONFIG: ark_can-rtk-gps_default
    ark_can-rtk-gps_debug:
      short: ark_can-rtk-gps_debug
      buildType: MinSizeRel
      settings:
        CONFIG: ark_can-rtk-gps_debug
    ark_can-rtk-gps_canbootloader:
      short: ark_can-rtk-gps_canbootloader
      buildType: MinSizeRel
      settings:
        CONFIG: ark_can-rtk-gps_canbootloader
    ark_septentrio-gps_default:
      short: ark_septentrio-gps_default
      buildType: MinSizeRel
      settings:
        CONFIG: ark_septentrio-gps_default
    ark_septentrio-gps_canbootloader:
      short: ark_septentrio-gps_canbootloader
      buildType: MinSizeRel
      settings:
        CONFIG: ark_septentrio-gps_canbootloader
    ark_teseo-gps_canbootloader:
      short: ark_teseo-gps_canbootloader
      buildType: MinSizeRel
      settings:
        CONFIG: ark_teseo-gps_canbootloader
    ark_cannode_default:
      short: ark_cannode_default
      buildType: MinSizeRel
      settings:
        CONFIG: ark_cannode_default
    ark_cannode_canbootloader:
      short: ark_cannode_canbootloader
      buildType: MinSizeRel
      settings:
        CONFIG: ark_cannode_canbootloader
    ark_fmu-v6x_bootloader:
      short: ark_fmu-v6x_bootloader
      buildType: MinSizeRel
      settings:
        CONFIG: ark_fmu-v6x_bootloader
    ark_fmu-v6x_default:
      short: ark_fmu-v6x_default
      buildType: MinSizeRel
      settings:
        CONFIG: ark_fmu-v6x_default
    ark_fpv_bootloader:
      short: ark_fpv_bootloader
      buildType: MinSizeRel
      settings:
        CONFIG: ark_fpv_bootloader
    ark_fpv_default:
      short: ark_fpv_default
      buildType: MinSizeRel
      settings:
        CONFIG: ark_fpv_default
    ark_pi6x_bootloader:
      short: ark_pi6x_bootloader
      buildType: MinSizeRel
      settings:
        CONFIG: ark_pi6x_bootloader
    ark_pi6x_default:
      short: ark_pi6x_default
      buildType: MinSizeRel
      settings:
        CONFIG: ark_pi6x_default
    atl_mantis-edu_default:
      short: atl_mantis-edu
      buildType: MinSizeRel
      settings:
        CONFIG: atl_mantis-edu_default
    av_x-v1_default:
      short: av_x-v1
      buildType: MinSizeRel
      settings:
        CONFIG: av_x-v1_default
    bitcraze_crazyflie_default:
      short: bitcraze_crazyflie
      buildType: MinSizeRel
      settings:
        CONFIG: bitcraze_crazyflie_default
    bluerobotics_navigator_default:
      short: bluerobotics_navigator
      buildType: MinSizeRel
      settings:
        CONFIG: bluerobotics_navigator_default
    cuav_can-gps-v1_default:
      short: cuav_can-gps-v1_default
      buildType: MinSizeRel
      settings:
        CONFIG: cuav_can-gps-v1_default
    cuav_can-gps-v1_canbootloader:
      short: cuav_can-gps-v1_canbootloader
      buildType: MinSizeRel
      settings:
        CONFIG: cuav_can-gps-v1_canbootloader
    cuav_nora_default:
      short: cuav_nora
      buildType: MinSizeRel
      settings:
        CONFIG: cuav_nora_default
    cuav_x7pro_default:
      short: cuav_x7pro
      buildType: MinSizeRel
      settings:
        CONFIG: cuav_x7pro_default
    cuav_7-nano_default:
      short: cuav_7-nano
      buildType: MinSizeRel
      settings:
        CONFIG: cuav_7-nano_default
    cubepilot_cubeorange_test:
      short: cubepilot_cubeorange
      buildType: MinSizeRel
      settings:
        CONFIG: cubepilot_cubeorange_test
    cubepilot_cubeorangeplus_test:
      short: cubepilot_cubeorangeplus
      buildType: MinSizeRel
      settings:
        CONFIG: cubepilot_cubeorangeplus_test
    emlid_navio2_default:
      short: emlid_navio2
      buildType: MinSizeRel
      settings:
        CONFIG: emlid_navio2_default
    freefly_can-rtk-gps_default:
      short: freefly_can-rtk-gps_default
      buildType: MinSizeRel
      settings:
        CONFIG: freefly_can-rtk-gps_default
    freefly_can-rtk-gps_canbootloader:
      short: freefly_can-rtk-gps_canbootloader
      buildType: MinSizeRel
      settings:
        CONFIG: freefly_can-rtk-gps_canbootloader
    holybro_can-gps-v1_canbootloader:
      short: holybro_can-gps-v1_canbootloader
      buildType: MinSizeRel
      settings:
        CONFIG: holybro_can-gps-v1_canbootloader
    holybro_can-gps-v1_default:
      short: holybro_can-gps-v1_default
      buildType: MinSizeRel
      settings:
        CONFIG: holybro_can-gps-v1_default
    holybro_durandal-v1_default:
      short: holybro_durandal-v1
      buildType: MinSizeRel
      settings:
        CONFIG: holybro_durandal-v1_default
    holybro_kakuteh7-wing_default:
      short: holybro_kakuteh7-wing
      buildType: MinSizeRel
      settings:
        CONFIG: holybro_kakuteh7-wing_default
    holybro_kakuteh7dualimu_default:
      short: holybro_kakuteh7dualimu
      buildType: MinSizeRel
      settings:
        CONFIG: holybro_kakuteh7dualimu_default
    matek_h743-slim_default:
      short: matek_h743-slim
      buildType: MinSizeRel
      settings:
        CONFIG: matek_h743-slim_default
    matek_gnss-m9n-f4_canbootloader:
      short: matek_gnss-m9n-f4_canbootloader
      buildType: MiniSizeRel
      settings:
        CONFIG: matek_m9nf4can_canbootloader
    matek_gnss-m9n-f4_default:
      short: matek_gnss-m9n-f4_default
      buildType: MiniSizeRel
      settings:
        CONFIG: matek_gnss-m9n-f4_default
    micoair_h743_bootloader:
      short: micoair_h743_bootloader
      buildType: MinSizeRel
      settings:
        CONFIG: micoair_h743_bootloader
    micoair_h743_default:
      short: micoair_h743
      buildType: MinSizeRel
      settings:
        CONFIG: micoair_h743_default
    micoair_h743-aio_bootloader:
      short: micoair_h743-aio_bootloader
      buildType: MinSizeRel
      settings:
        CONFIG: micoair_h743-aio_bootloader
    micoair_h743-aio_default:
      short: micoair_h743-aio
      buildType: MinSizeRel
      settings:
        CONFIG: micoair_h743-aio_default
    micoair_h743-v2_bootloader:
      short: micoair_h743-v2_bootloader
      buildType: MinSizeRel
      settings:
        CONFIG: micoair_h743-v2_bootloader
    micoair_h743-v2_default:
      short: micoair_h743-v2
      buildType: MinSizeRel
      settings:
        CONFIG: micoair_h743-v2_default
    modalai_fc-v1_default:
      short: modalai_fc-v1
      buildType: MinSizeRel
      settings:
        CONFIG: modalai_fc-v1_default
    modalai_fc-v2_default:
      short: modalai_fc-v2
      buildType: MinSizeRel
      settings:
        CONFIG: modalai_fc-v2_default
    modalai_voxl2-io_default:
      short: modalai_voxl2-io
      buildType: MinSizeRel
      settings:
        CONFIG: modalai_voxl2-io_default
    mro_ctrl-zero-f7_default:
      short: mro_ctrl-zero-f7
      buildType: MinSizeRel
      settings:
        CONFIG: mro_ctrl-zero-f7_default
    mro_pixracerpro_bootloader:
      short: mro_pixracerpro_bootloader
      buildType: MinSizeRel
      settings:
        CONFIG: mro_pixracerpro_bootloader
    mro_pixracerpro_default:
      short: mro_pixracerpro_default
      buildType: MinSizeRel
      settings:
        CONFIG: mro_pixracerpro_default
    mro_x21-777_default:
      short: mro_x2.1-777
      buildType: MinSizeRel
      settings:
        CONFIG: mro_x21-777_default
    nxp_fmuk66-v3_default:
      short: nxp_fmuk66-v3
      buildType: MinSizeRel
      settings:
        CONFIG: nxp_fmuk66-v3_default
    nxp_mr-canhubk3_default:
      short: nxp_mr-canhubk3_default
      buildType: MinSizeRel
      settings:
        CONFIG: nxp_mr-canhubk3_default
    nxp_mr-canhubk3_fmu:
      short: nxp_mr-canhubk3_fmu
      buildType: MinSizeRel
      settings:
        CONFIG: nxp_mr-canhubk3_fmu
    nxp_tropic-community_default:
      short: nxp_tropic-community_default
      buildType: MinSizeRel
      settings:
        CONFIG: nxp_tropic-community_default
    raspberrypi_pico_default:
      short: raspberrypi_pico
      buildType: MinSizeRel
      settings:
        CONFIG: raspberrypi_pico_default
    zeroone_x6_default:
      short: zeroone_x6
      buildType: MinSizeRel
      settings:
        CONFIG: zeroone_x6_default
    zeroone_x6_bootloader:
      short: zeroone_x6_bootloader
      buildType: MinSizeRel
      settings:
        CONFIG: zeroone_x6_bootloader
    x-mav_ap-h743v2_bootloader:
      short: x-mav_ap-h743v2-boot
      buildType: MinSizeRel
      settings:
        CONFIG: x-mav_ap-h743v2_bootloader
    x-mav_ap-h743v2_default:
      short: x-mav_ap-h743v2
      buildType: MinSizeRel
      settings:
        CONFIG: x-mav_ap-h743v2_default
