{
    // See http://go.microsoft.com/fwlink/?LinkId=827846
    // for the documentation about the extensions.json format
    "recommendations": [
        "chiehyu.vscode-astyle",
        "dan-c-underwood.arm",
        "editorconfig.editorconfig",
        "fredericbonnet.cmake-test-adapter",
        "github.vscode-pull-request-github",
        "marus25.cortex-debug",
        "ms-azuretools.vscode-docker",
        "ms-iot.vscode-ros",
        "ms-python.python",
        "ms-vscode.cmake-tools",
        "ms-vscode.cpptools",
        "ms-vscode.cpptools-extension-pack",
        "redhat.vscode-yaml",
        "streetsidesoftware.code-spell-checker",
        "twxs.cmake",
        "uavcan.dsdl",
        "wholroyd.jinja",
        "zixuanwang.linkerscript",
        "ms-vscode.makefile-tools"
    ]
}
