{
        "astyle.astylerc": "${workspaceFolder}/Tools/astyle/astylerc",
        "astyle.c.enable": true,
        "astyle.cpp.enable": true,
        "C_Cpp.autoAddFileAssociations": false,
        "C_Cpp.clang_format_fallbackStyle": "none",
        "C_Cpp.default.browse.limitSymbolsToIncludedHeaders": true,
        "C_Cpp.default.cppStandard": "c++14",
        "C_Cpp.default.cStandard": "c11",
        "C_Cpp.formatting": "Disabled",
        "C_Cpp.intelliSenseEngine": "Default",
        "C_Cpp.vcpkg.enabled": false,
        "C_Cpp.workspaceParsingPriority": "low",
        "cmake.buildBeforeRun": true,
        "cmake.buildDirectory": "${workspaceFolder}/build/${variant:CONFIG}",
        "cmake.configureOnOpen": true,
        "cmake.ctest.parallelJobs": 1,
        "cmake.options.statusBarVisibility": "compact",
        "cmake.skipConfigureIfCachePresent": true,
        "cmakeExplorer.buildDir": "${workspaceFolder}/build/px4_sitl_test",
        "cmakeExplorer.parallelJobs": 1,
        "cmakeExplorer.suiteDelimiter": "-",
        "cSpell.allowCompoundWords": true,
        "cSpell.diagnosticLevel": "Hint",
        "cSpell.showStatus": false,
        "cSpell.words": [
                "acro",
                "nuttx",
                "esc"
        ],
        "debug.toolBarLocation": "docked",
        "editor.defaultFormatter": "chiehyu.vscode-astyle",
        "editor.insertSpaces": false,
        "editor.minimap.maxColumn": 120,
        "editor.minimap.renderCharacters": false,
        "editor.minimap.showSlider": "always",
        "editor.suggest.localityBonus": true,
        "editor.tabSize": 8,
        "editor.wordWrapColumn": 120,
        "explorer.openEditors.visible": 0,
        "files.insertFinalNewline": true,
        "files.trimTrailingWhitespace": true,
        "files.watcherExclude": {
                "**/build/**": true
        },
        "git.detectSubmodulesLimit": 20,
        "git.ignoreLimitWarning": true,
        "githubPullRequests.defaultMergeMethod": "squash",
        "githubPullRequests.telemetry.enabled": false,
        "files.associations": {
                "*.jinja": "jinja",
                "algorithm": "cpp",
                "array": "cpp",
                "atomic": "cpp",
                "bitset": "cpp",
                "cctype": "cpp",
                "cfenv": "cpp",
                "chrono": "cpp",
                "cinttypes": "cpp",
                "clocale": "cpp",
                "cmath": "cpp",
                "codecvt": "cpp",
                "complex": "cpp",
                "condition_variable": "cpp",
                "csignal": "cpp",
                "cstdarg": "cpp",
                "cstddef": "cpp",
                "cstdint": "cpp",
                "cstdio": "cpp",
                "cstdlib": "cpp",
                "cstring": "cpp",
                "ctime": "cpp",
                "cwchar": "cpp",
                "cwctype": "cpp",
                "deque": "cpp",
                "exception": "cpp",
                "forward_list": "cpp",
                "fstream": "cpp",
                "functional": "cpp",
                "future": "cpp",
                "hash_map": "cpp",
                "hash_set": "cpp",
                "initializer_list": "cpp",
                "iomanip": "cpp",
                "iosfwd": "cpp",
                "iostream": "cpp",
                "istream": "cpp",
                "iterator": "cpp",
                "limits": "cpp",
                "list": "cpp",
                "map": "cpp",
                "memory": "cpp",
                "memory_resource": "cpp",
                "mutex": "cpp",
                "new": "cpp",
                "numeric": "cpp",
                "optional": "cpp",
                "ostream": "cpp",
                "random": "cpp",
                "ratio": "cpp",
                "regex": "cpp",
                "set": "cpp",
                "sstream": "cpp",
                "stdexcept": "cpp",
                "streambuf": "cpp",
                "string": "cpp",
                "string_view": "cpp",
                "strstream": "cpp",
                "system_error": "cpp",
                "thread": "cpp",
                "tuple": "cpp",
                "type_traits": "cpp",
                "typeindex": "cpp",
                "typeinfo": "cpp",
                "unordered_map": "cpp",
                "unordered_set": "cpp",
                "utility": "cpp",
                "valarray": "cpp",
                "variant": "cpp",
                "vector": "cpp",
                "Jenkinsfile*": "groovy",
                "*.sdf": "xml"
        },
        "search.exclude": {
                "${workspaceFolder}/build": true
        },
        "search.showLineNumbers": true,
        "terminal.integrated.scrollback": 15000,
        "yaml.schemas": {
                "${workspaceFolder}/validation/module_schema.yaml": "${workspaceFolder}/src/modules/*/module.yaml"
        },
}
