{
    // See https://go.microsoft.com/fwlink/?LinkId=733558
    // for the documentation about the tasks.json format
    "version": "2.0.0",
    "tasks": [
        {
            "label": "build",
            "type": "shell",
            "command": "/bin/bash",
            "args": [
                "-c",
                "${command:cmake.tasksBuildCommand}"
            ],
            "options": {
                "cwd": "${command:cmake.buildDirectory}"
            },
            "group": {
                "kind": "build",
                "isDefault": true,
            },
            "problemMatcher": {
                "base": "$gcc",
                "fileLocation": ["relative", "${command:cmake.buildDirectory}"]
            },
            "presentation":{
                "echo": false,
                "showReuseMessage": false,
                "clear": true,
                "panel": "shared",
                "group": "build"
            }
        },
        {
            "label": "test",
            "type": "shell",
            "command": "make tests",
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "group": {
                "kind": "test",
                "isDefault": true,
            },
            "presentation":{
                "echo": true,
                "showReuseMessage": false,
                "clear": false,
                "panel": "shared",
                "group": "test"
            }
        },
        {
            "label": "jmavsim kill",
            "type": "shell",
            "command": "kill $(ps aux | grep jmavsim | grep -v 'grep' | awk '{print $2}') || true",
            "presentation": {
                "echo": true,
                "reveal": "never",
                "revealProblems": "onProblem",
                "focus": false,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": false,
                "close": true
            },
            "problemMatcher": [],
            "dependsOn":["px4_sitl_cleanup"]
        },
        {
            "label": "gazebo-classic build",
            "type": "shell",
            "command": "make px4_sitl_default sitl_gazebo-classic",
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "echo": true,
                "reveal": "never",
                "revealProblems": "onProblem",
                "focus": false,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": false,
                "close": true
            },
            "problemMatcher": [],
        },
        {
            "label": "gazebo-classic start",
            "type": "shell",
            "dependsOn": "gazebo-classic build",
            "options": {
                "cwd": "${workspaceFolder}",
                "env": {
                    "GAZEBO_PLUGIN_PATH": "${workspaceFolder}/build/px4_sitl_default/build_gazebo-classic",
                    "GAZEBO_MODEL_PATH": "${workspaceFolder}/Tools/simulation/gazebo-classic/sitl_gazebo-classic/models",
                    "PX4_SIM_SPEED_FACTOR": "1"
                }
            },
            "command": "gzserver --verbose ${workspaceFolder}/Tools/simulation/gazebo-classic/sitl_gazebo-classic/worlds/empty.world",
            "isBackground": true,
            "presentation": {
                "echo": true,
                "reveal": "never",
                "revealProblems": "onProblem",
                "focus": false,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": false,
                "close": true
            },
            "problemMatcher": [
                {
                    "pattern": [
                        {
                            "regexp": ".",
                            "file": 1,
                            "location": 2,
                            "message": 3
                        }
                    ],
                    "background": {
                        "activeOnStart": true,
                        "beginsPattern": ".",
                        "endsPattern": ".",
                    }
                }
            ]
        },
        {
            "label": "gazebo-classic",
            "type": "shell",
            "dependsOn": "gazebo-classic start",
            "options": {
                "cwd": "${workspaceFolder}",
                "env": {
                    "GAZEBO_PLUGIN_PATH": "${workspaceFolder}/build/px4_sitl_default/build_gazebo-classic",
                    "GAZEBO_MODEL_PATH": "${workspaceFolder}/Tools/simulation/gazebo-classic/sitl_gazebo-classic/models",
                    "PX4_SIM_SPEED_FACTOR": "1"
                }
            },
            "command": "gz model --verbose --spawn-file=${workspaceFolder}/Tools/simulation/gazebo-classic/sitl_gazebo-classic/models/iris/iris.sdf --model-name=iris -x 1.01 -y 0.98 -z 0.83",
            "isBackground": false,
            "presentation": {
                "echo": true,
                "reveal": "never",
                "revealProblems": "onProblem",
                "focus": false,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": false,
                "close": true
            },
            "problemMatcher": [
                {
                    "pattern": [
                        {
                            "regexp": ".",
                            "file": 1,
                            "location": 2,
                            "message": 3
                        }
                    ],
                    "background": {
                        "activeOnStart": true,
                        "beginsPattern": ".",
                        "endsPattern": ".",
                    }
                }
            ]
        },
        {
            "label": "gazebo",
            "type": "shell",
            "options": {
                "cwd": "${workspaceFolder}",
                "env": {
                    "IGN_GAZEBO_RESOURCE_PATH": "${workspaceFolder}/Tools/simulation/gz/models",
                }
            },
            "command": "gz sim -v 4 -r ${workspaceFolder}/Tools/simulation/gz/worlds/${input:gzWorld}.sdf",
            "isBackground": true,
            "presentation": {
                "echo": true,
                "reveal": "always",
                "revealProblems": "onProblem",
                "focus": true,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": false,
                "close": false
            },
            "problemMatcher": [],
            "dependsOn":["gazebo kill"]
        },
        {
            "label": "gazebo-classic kill",
            "type": "shell",
            "command": "pkill -9 -f gzserver || true",
            "presentation": {
                "echo": true,
                "reveal": "never",
                "revealProblems": "onProblem",
                "focus": false,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": false,
                "close": true
            },
            "problemMatcher": [],
            "dependsOn":["px4_sitl_cleanup"]
        },
        {
            "label": "gazebo kill",
            "type": "shell",
            "command": "pkill -9 -f 'gz sim' || true",
            "presentation": {
                "echo": true,
                "reveal": "never",
                "revealProblems": "onProblem",
                "focus": false,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": false,
                "close": true
            },
            "problemMatcher": [],
            "dependsOn":["px4_sitl_cleanup"]
        },

        {
            "label": "px4_sitl_cleanup",
            "type": "shell",
            "command": "rm -rfv /tmp/px4* || true",
            "presentation": {
                "echo": true,
                "reveal": "never",
                "revealProblems": "onProblem",
                "focus": false,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": false,
                "close": true
            },
            "problemMatcher": [],
            "dependsOn":["px4_kill"]
        },
        {
            "label": "px4_kill",
            "type": "shell",
            "command": "pkill -9 px4 || true",
            "presentation": {
                "echo": true,
                "reveal": "never",
                "revealProblems": "onProblem",
                "focus": false,
                "panel": "dedicated",
                "showReuseMessage": false,
                "clear": false,
                "close": true
            },
            "problemMatcher": []
        },
        {
            "label": "mavlink shell",
            "type": "shell",
            "command": "./Tools/mavlink_shell.py",
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": []
        },
        {
            "label": "miniterm.py",
            "type": "shell",
            "command": "miniterm.py --raw - 57600",
            "options": {
                "cwd": "${workspaceFolder}"
            },
            "presentation": {
                "echo": true,
                "reveal": "always",
                "focus": true,
                "panel": "new",
                "showReuseMessage": false,
                "clear": false
            },
            "problemMatcher": []
        }
    ],
    "inputs": [
        {
            "type": "pickString",
            "id": "gzWorld",
            "description": "gz world",
            "options": [
                "default"
            ],
            "default": "default"
        }
    ]
}
