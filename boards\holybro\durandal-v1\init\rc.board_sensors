#!/bin/sh
#
# Holybro Durandal V1 specific board sensors init
#------------------------------------------------------------------------------
board_adc start

# Internal SPI bus ICM-20689
icm20689 -R 2 -s start

# Internal SPI BMI088
bmi088 -A -R 2 -s start
bmi088 -G -R 2 -s start

if ver hwtypecmp  VD000000
then
	# Internal SPI BMI088
	bmi088 -A -R 2 -s start
	bmi088 -G -R 2 -s start
fi
if ver hwtypecmp  VD000001
then
	# Internal SPI ICM-20602
	icm20602 -R 2 -s start
fi

# internal compass
ist8310 -I -R 10 start

# Baro on internal SPI
ms5611 -s start

# External compass on GPS1/I2C1: standard Holybro GPS/compass puck (with lights, safety button, and buzzer)
ist8310 -X -b 1 -R 10 start
