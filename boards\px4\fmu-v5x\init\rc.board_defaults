#!/bin/sh
#
# board specific defaults
#------------------------------------------------------------------------------

param set-default SENS_EN_INA238 0
param set-default SENS_EN_INA228 0
param set-default SENS_EN_INA226 1

if ver hwbasecmp 008 009 00a 010 011
then
	# Skynode: use the "custom participant", IP=********** config for uxrce_dds_client
	param set-default UXRCE_DDS_PTCFG 2
	param set-default UXRCE_DDS_AG_IP 170461697
	param set-default UXRCE_DDS_CFG 1000

	# The buzzer draws too much power (0.2A) on the GPS power rail (limit 0.45A).
	param set-default CBRK_BUZZER 782097
else
	# Mavlink ethernet (CFG 1000)
	param set-default MAV_2_CONFIG 1000
	param set-default MAV_2_BROADCAST 1
	param set-default MAV_2_MODE 0
	param set-default MAV_2_RADIO_CTL 0
	param set-default MAV_2_RATE 100000
	param set-default MAV_2_REMOTE_PRT 14550
	param set-default MAV_2_UDP_PRT 14550
fi

safety_button start

# GPIO Expander driver on external I2C3
if ver hwbasecmp 009
then
	# No USB
	mcp23009 start -b 3 -X -D 0xf0 -O 0xf0 -P 0x0f -U 10
fi
if ver hwbasecmp 00a 008
then
	mcp23009 start -b 3 -X -D 0xf1 -O 0xf0 -P 0x0f -U 10
fi
