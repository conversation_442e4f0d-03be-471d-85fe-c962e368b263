#!/bin/sh
#
# PX4 FMUv5X specific board sensors init
#------------------------------------------------------------------------------

set HAVE_PM2 yes

if mft query -q -k MFT -s MFT_PM2 -v 0
then
	set HAVE_PM2 no
fi
if param compare -s ADC_ADS1115_EN 1
then
	ads1115 start -X
	board_adc start -n
else
	board_adc start
fi


if param compare SENS_EN_INA226 1
then
	# Start Digital power monitors
	ina226 -X -b 1 -t 1 -k start

	if [ $HAVE_PM2 = yes ]
	then
		ina226 -X -b 2 -t 2 -k start
	fi
fi

if param compare SENS_EN_INA228 1
then
	# Start Digital power monitors
	ina228 -X -b 1 -t 1 -k start
	if [ $HAVE_PM2 = yes ]
	then
		ina228 -X -b 2 -t 2 -k start
	fi
fi

if param compare SENS_EN_INA238 1
then
	# Start Digital power monitors
	ina238 -X -b 1 -t 1 -k start
	if [ $HAVE_PM2 = yes ]
	then
		ina238 -X -b 2 -t 2 -k start
	fi
fi

if ver hwbasecmp 008 009 00a 010 011
then
	#SKYNODE base fmu board orientation

	if ver hwtypecmp V5X000 V5X001
	then
		# Internal SPI BMI088
		bmi088 -A -R 2 -s start
		bmi088 -G -R 2 -s start
	else
		# Internal SPI bus ICM20649
		icm20649 -s -R 4 start
	fi

	# Internal SPI bus ICM42688p
	icm42688p -R 4 -s start

	# Internal SPI bus ICM-20602 (hard-mounted)
	icm20602 -R 8 -s start

	# Internal magnetometer on I2c
	bmm150 -I -R 6 start

	# Auto start power monitors
	pm_selector_auterion start

	# Auterion's INA238 uses a shunt value of 0.0003 instead of 0.0005.
	param set-default INA238_SHUNT 0.0003
else
	#FMUv5Xbase board orientation

	if ver hwtypecmp V5X000 V5X001
	then
		# Internal SPI BMI088
		bmi088 -A -R 4 -s start
		bmi088 -G -R 4 -s start
	else
		# Internal SPI bus ICM20649
		icm20649 -s -R 6 start
	fi

	# Internal SPI bus ICM42688p
	icm42688p -R 6 -s start

	# Internal SPI bus ICM-20602 (hard-mounted)
	icm20602 -R 10 -s start

	# Internal magnetometer on I2c
	bmm150 -I start
fi

# External compass on GPS1/I2C1 (the 3rd external bus): standard Holybro Pixhawk 4 or CUAV V5 GPS/compass puck (with lights, safety button, and buzzer)
ist8310 -X -b 1 -R 10 start

# Possible internal Baro

# Disable startup of internal baros if param is set to false
if param compare SENS_INT_BARO_EN 1
then
	bmp388 -I -a 0x77 start
	if ver hwtypecmp V5X000
	then
		bmp388 -I start
	else
		bmp388 -X -b 2 start
	fi

fi
unset HAVE_PM2
