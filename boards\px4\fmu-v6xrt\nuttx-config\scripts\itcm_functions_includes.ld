/* Auto-generated */
*(.text._ZN4uORB7Manager27orb_add_internal_subscriberE6ORB_IDhPj)
*(.text._ZN13MavlinkStream6updateERKy)
*(.text._ZN7Mavlink16update_rate_multEv)
*(.text._ZN3sym17PredictCovarianceIfEEN6matrix6MatrixIT_Lj23ELj23EEERKNS2_IS3_Lj24ELj1EEERKS4_RKNS2_IS3_Lj3ELj1EEES3_SC_SC_S3_S3_) /* itcm-check-ignore */
*(.text._ZN13MavlinkStream12get_size_avgEv)
*(.text._ZN16ControlAllocator3RunEv)
*(.text._ZN22MulticopterRateControl3RunEv.part.0)
*(.text._ZN7Mavlink9task_mainEiPPc)
*(.text._ZN7sensors22VehicleAngularVelocity3RunEv)
*(.text._ZN4uORB12Subscription9subscribeEv.part.0)
*(.text._ZN4uORB7Manager13orb_data_copyEPvS1_Rjb)
*(.text._ZN4uORB10DeviceNode5writeEP4filePKcj)
*(.text._ZN4uORB10DeviceNode7publishEPK12orb_metadataPvPKv)
*(.text._ZN4uORB12DeviceMaster19getDeviceNodeLockedEPK12orb_metadatah)
*(.text._Z12get_orb_meta6ORB_ID)
*(.text._ZN9ICM42688P12ProcessAccelERKyPKN20InvenSense_ICM42688P4FIFO4DATAEh)
*(.text._ZN3px49WorkQueue3RunEv)
*(.text._ZN9ICM42688P11ProcessGyroERKyPKN20InvenSense_ICM42688P4FIFO4DATAEh)
*(.text._ZN4EKF23RunEv)
*(.text._ZN7sensors10VehicleIMU7PublishEv)
*(.text._ZN4math17WelfordMeanVectorIfLj3EE6updateERKN6matrix6VectorIfLj3EEE)
*(.text._ZN7sensors10VehicleIMU10UpdateGyroEv)
*(.text._ZN9ICM42688P8FIFOReadERKyh)
*(.text._ZN3Ekf20controlGravityFusionERKN9estimator9imuSampleE)
*(.text._ZN16PX4Accelerometer10updateFIFOER19sensor_accel_fifo_s)
*(.text._ZN7sensors22VehicleAngularVelocity19CalibrateAndPublishERKyRKN6matrix7Vector3IfEES7_)
*(.text._ZN4uORB12Subscription10advertisedEv)
*(.text._ZNK15AttitudeControl6updateERKN6matrix10QuaternionIfEE)
*(.text._ZN7sensors10VehicleIMU11UpdateAccelEv)
*(.text.perf_set_elapsed.part.0)
*(.text._ZN4uORB12Subscription6updateEPv)
*(.text._ZN12PX4Gyroscope10updateFIFOER18sensor_gyro_fifo_s)
*(.text._ZN7sensors10VehicleIMU3RunEv)
*(.text.__aeabi_l2f)
*(.text._ZN39ControlAllocationSequentialDesaturation23computeDesaturationGainERKN6matrix6VectorIfLj16EEES4_)
*(.text.pthread_mutex_timedlock)
*(.text._ZN7sensors22VehicleAngularVelocity21FilterAngularVelocityEiPfi)
*(.text._ZN26MulticopterAttitudeControl3RunEv.part.0)
*(.text._ZN6device3SPI9_transferEPhS1_j)
*(.text._ZN15OutputPredictor21calculateOutputStatesEyRKN6matrix7Vector3IfEEfS4_f)
*(.text._ZN7sensors18VotedSensorsUpdate7imuPollER17sensor_combined_s)
*(.text._Z9rotate_3i8RotationRsS0_S0_)
*(.text.fs_getfilep)
*(.text.MEM_DataCopy0_1)
*(.text._ZN7sensors19VehicleAcceleration3RunEv)
*(.text.uart_ioctl)
*(.text._ZN26MulticopterPositionControl3RunEv.part.0)
*(.text.pthread_mutex_take)
*(.text._ZN14ImuDownSampler6updateERKN9estimator9imuSampleE)
*(.text._ZN39ControlAllocationSequentialDesaturation6mixYawEv)
*(.text._ZN16ControlAllocator25publish_actuator_controlsEv.part.0)
*(.text._ZN9ICM42688P7RunImplEv)
*(.text._ZN4uORB12Subscription9subscribeEv)
*(.text.param_get)
*(.text._ZN7sensors22VehicleAngularVelocity21SensorSelectionUpdateERKyb)
*(.text._ZN3px49WorkQueue3AddEPNS_8WorkItemE)
*(.text._ZN4EKF220PublishLocalPositionERKy)
*(.text._mav_finalize_message_chan_send)
*(.text._ZN7sensors22VehicleAngularVelocity16ParametersUpdateEb)
*(.text._ZN6events12SendProtocol6updateERKy)
*(.text._ZN6device3SPI8transferEPhS1_j)
*(.text._ZN27MavlinkStreamDistanceSensor4sendEv)
*(.text.hrt_call_internal)
*(.text._ZN39ControlAllocationSequentialDesaturation18mixAirmodeDisabledEv)
*(.text._ZN7Mavlink15get_free_tx_bufEv)
*(.text.nx_poll)
*(.text._ZN15MavlinkReceiver3runEv)
*(.text._ZN9ICM42688P18ProcessTemperatureEPKN20InvenSense_ICM42688P4FIFO4DATAEh)
*(.text._ZN15OutputPredictor19correctOutputStatesEyRKN6matrix10QuaternionIfEERKNS0_7Vector3IfEERK9LatLonAltS8_S8_)
*(.text._ZN3Ekf12predictStateERKN9estimator9imuSampleE)
*(.text._ZN3px46logger6Logger3runEv)
*(.text._ZN4uORB20SubscriptionInterval7updatedEv)
*(.text._ZN24MavlinkStreamCommandLong4sendEv)
*(.text._ZN9Commander3runEv)
*(.text._ZN3Ekf17predictCovarianceERKN9estimator9imuSampleE)
*(.text.wd_cancel)
*(.text._ZN7Sensors3RunEv)
*(.text.perf_end)
*(.text._ZN4uORB12Subscription7updatedEv)
*(.text._ZN13land_detector12LandDetector3RunEv)
*(.text.sched_idletask)
*(.text.atanf)
*(.text.uart_write)
*(.text.pthread_mutex_unlock)
*(.text.__ieee754_asinf)
*(.text.MEM_DataCopy0_2)
*(.text._ZN20MavlinkCommandSender13check_timeoutE17mavlink_channel_t)
*(.text._ZN16ControlAllocator32publish_control_allocator_statusEi)
*(.text.__ieee754_atan2f)
*(.text._ZNK18DynamicSparseLayer3getEt)
*(.text.__udivmoddi4)
*(.text._ZN8Failsafe17checkStateAndModeERKyRKN12FailsafeBase5StateERK16failsafe_flags_s)
*(.text._ZN29MavlinkStreamHygrometerSensor4sendEv)
*(.text.pthread_mutex_give)
*(.text._ZN3Ekf18controlFusionModesERKN9estimator9imuSampleE)
*(.text._ZN4cdev4CDev11poll_notifyEm)
*(.text.file_vioctl)
*(.text._ZN7sensors18VotedSensorsUpdate11sensorsPollER17sensor_combined_s)
*(.text.nxsig_nanosleep)
*(.text.imxrt_lpspi1select)
*(.text.sem_wait)
*(.text.perf_count_interval.part.0)
*(.text._ZN16ControlAllocator37update_effectiveness_matrix_if_neededE25EffectivenessUpdateReason)
*(.text.MEM_LongCopyJump)
*(.text.px4_arch_adc_sample)
*(.text._ZN31MulticopterHoverThrustEstimator3RunEv)
*(.text._ZNK17ControlAllocation20clipActuatorSetpointERN6matrix6VectorIfLj16EEE)
*(.text._ZN4uORB7Manager17get_device_masterEv)
*(.text._ZN13DataValidator3putEyPKfmh)
*(.text.cdcuart_ioctl)
*(.text.cdcacm_sndpacket)
*(.text._ZN7sensors22VehicleAngularVelocity16SensorBiasUpdateEb)
*(.text._ZN13MavlinkStream11update_dataEv)
*(.text._ZN7sensors18VotedSensorsUpdate21calcGyroInconsistencyEv)
*(.text.param_set_used)
*(.text._ZN18EstimatorInterface10setIMUDataERKN9estimator9imuSampleE)
*(.text._ZN18DataValidatorGroup8get_bestEyPi)
*(.text._ZN4EKF218PublishInnovationsERKy)
*(.text._ZN21MavlinkMissionManager4sendEv)
*(.text._ZN22MulticopterRateControl28updateActuatorControlsStatusERK25vehicle_torque_setpoint_sf)
*(.text._ZN11RateControl6updateERKN6matrix7Vector3IfEES4_S4_fb)
*(.text._ZN39ControlAllocationSequentialDesaturation19desaturateActuatorsERN6matrix6VectorIfLj16EEERKS2_b)
*(.text.imxrt_lpi2c_transfer)
*(.text.uart_putxmitchar)
*(.text.clock_nanosleep)
*(.text.up_release_pending)
*(.text.MEM_DataCopy0)
*(.text._ZN22MavlinkStreamGPSRawInt4sendEv)
*(.text.dq_rem)
*(.text._ZN15GyroCalibration3RunEv.part.0)
*(.text._ZN7sensors18VotedSensorsUpdate22calcAccelInconsistencyEv)
*(.text._ZN24MavlinkStreamADSBVehicle4sendEv)
*(.text.sinf)
*(.text.hrt_call_after)
*(.text._ZN39ControlAllocationSequentialDesaturation8allocateEv)
*(.text.up_invalidate_dcache)
*(.text._ZN15PositionControl16_velocityControlEf)
*(.text._ZN4EKF222PublishAidSourceStatusERKy)
*(.text._ZN4ListIP13MavlinkStreamE8IteratorppEv)
*(.text._ZN20MavlinkStreamESCInfo4sendEv)
*(.text.sem_post)
*(.text._ZN3px417ScheduledWorkItem15ScheduleDelayedEm)
*(.text._ZN10FlightTaskC1Ev)
*(.text.usleep)
*(.text._ZN14FlightTaskAutoC1Ev)
*(.text.sem_getvalue)
*(.text._ZN23MavlinkStreamHighresIMU4sendEv)
*(.text.imxrt_gpio_write)
*(.text._ZN3Ekf6updateEv)
*(.text.__ieee754_acosf)
*(.text._ZN3Ekf20updateIMUBiasInhibitERKN9estimator9imuSampleE)
*(.text._ZN9Commander13dataLinkCheckEv)
*(.text._ZN17FlightModeManager10switchTaskE15FlightTaskIndex)
*(.text._ZN12PX4Gyroscope9set_scaleEf)
*(.text._ZN12FailsafeBase6updateERKyRKNS_5StateEbbRK16failsafe_flags_s)
*(.text._ZN18MavlinkStreamDebug4sendEv)
*(.text._ZN27MavlinkStreamServoOutputRawILi0EE4sendEv)
*(.text.asinf)
*(.text._ZN6matrix5EulerIfEC1ERKNS_3DcmIfEE)
*(.text._ZN4EKF227PublishInnovationTestRatiosERKy)
*(.text._ZN4EKF213PublishStatusERKy)
*(.text._ZN4EKF226PublishInnovationVariancesERKy)
*(.text._ZN13land_detector23MulticopterLandDetector25_get_ground_contact_stateEv)
*(.text.imxrt_dmach_start)
*(.text._ZN3ADC19update_system_powerEy)
*(.text._ZNK3Ekf19get_ekf_soln_statusEv)
*(.text._ZN3px46logger15watchdog_updateERNS0_15watchdog_data_tEb)
*(.text.imxrt_gpio_read)
*(.text._ZN32MavlinkStreamNavControllerOutput4sendEv)
*(.text._ZN15ArchPX4IOSerial13_bus_exchangeEP8IOPacket)
*(.text._ZN39MavlinkStreamGimbalDeviceAttitudeStatus4sendEv)
*(.text._ZNK10ConstLayer3getEt)
*(.text.__aeabi_uldivmod)
*(.text.up_udelay)
*(.text.up_idle)
*(.text._ZN20MavlinkStreamGPS2Raw4sendEv)
*(.text._ZN4EKF217UpdateCalibrationERKyRNS_19InFlightCalibrationERKN6matrix7Vector3IfEES8_fbb)
*(.text._ZN28MavlinkStreamGpsGlobalOrigin4sendEv)
*(.text._ZN11ControlMath15bodyzToAttitudeEN6matrix7Vector3IfEEfR27vehicle_attitude_setpoint_s)
*(.text._ZN4EKF217UpdateRangeSampleER17ekf2_timestamps_s)
*(.text._ZN3Ekf24controlOpticalFlowFusionERKN9estimator9imuSampleE)
*(.text._ZN19MavlinkStreamRawRpm4sendEv)
*(.text._ZN13MavlinkStream10const_rateEv)
*(.text._ZN4EKF215PublishOdometryERKyRKN9estimator9imuSampleE)
*(.text._ZN15FailureDetector20updateAttitudeStatusERK16vehicle_status_s)
*(.text._ZN7Mavlink19configure_sik_radioEv)
*(.text._ZN13BatteryStatus8adc_pollEv)
*(.text.getpid)
*(.text._ZN13DataValidator10confidenceEy)
*(.text._ZN22MavlinkStreamGPSStatus4sendEv)
*(.text._ZN4EKF220UpdateAirspeedSampleER17ekf2_timestamps_s)
*(.text._ZN23MavlinkStreamStatustext4sendEv)
*(.text._ZN12PX4IO_serial4readEjPvj)
*(.text.uart_poll)
*(.text._ZN24MavlinkParametersManager4sendEv)
*(.text._ZN26MulticopterPositionControl18set_vehicle_statesERK24vehicle_local_position_sf)
*(.text.file_poll)
*(.text.hrt_elapsed_time)
*(.text._ZN7Mavlink11send_finishEv)
*(.text._ZNK3Ekf36estimateInertialNavFallingLikelihoodEv)
*(.text._ZN15PositionControl16_positionControlEv)
*(.text._ZN28MavlinkStreamDebugFloatArray4sendEv)
*(.text._ZN11ControlMath9limitTiltERN6matrix7Vector3IfEERKS2_f)
*(.text.pthread_mutex_lock)
*(.text._ZN21MavlinkStreamAltitude8get_sizeEv)
*(.text._ZN7Mavlink29check_requested_subscriptionsEv)
*(.text.imxrt_lpspi_setmode)
*(.text._ZN3Ekf34controlZeroInnovationHeadingUpdateEv)
*(.text.perf_begin)
*(.text.imxrt_lpspi_setfrequency)
*(.text._ZN17FlightModeManager9_initTaskE15FlightTaskIndex)
*(.text._ZN22MulticopterRateControl3RunEv)
*(.text.cosf)
*(.text._ZN22MavlinkStreamESCStatus4sendEv)
*(.text._ZN26MavlinkStreamCameraTrigger4sendEv)
*(.text._ZN36MavlinkStreamPositionTargetGlobalInt4sendEv)
*(.text._ZN4uORB12Subscription4copyEPv)
*(.text._ZN7sensors19VehicleAcceleration21SensorSelectionUpdateEb)
*(.text.crc_accumulate)
*(.text._ZN3px46logger6Logger13update_paramsEv)
*(.text._ZN11calibration14DeviceExternalEm)
*(.text._ZN25MavlinkStreamHomePosition8get_sizeEv)
*(.text.imxrt_lpspi_modifyreg32)
*(.text._ZN7sensors19VehicleAcceleration16SensorBiasUpdateEb)
*(.text.modifyreg32)
*(.text._ZNK6matrix6MatrixIfLj3ELj1EEmlEf)
*(.text._ZN6matrix5EulerIfEC1ERKNS_10QuaternionIfEE)
*(.text.imxrt_queuedtd)
*(.text._ZN27MavlinkStreamDistanceSensor8get_sizeEv)
*(.text._ZN3Ekf23controlBaroHeightFusionERKN9estimator9imuSampleE)
*(.text._ZN16PX4Accelerometer9set_scaleEf)
*(.text._ZN11ControlMath11constrainXYERKN6matrix7Vector2IfEES4_RKf)
*(.text._ZN22MavlinkStreamEfiStatus4sendEv)
*(.text._ZN22MavlinkStreamDebugVect4sendEv)
*(.text._ZN4EKF217PublishSensorBiasERKy)
*(.text._ZN17FlightModeManager3RunEv)
*(.text._ZN15PositionControl11_inputValidEv)
*(.text._ZN7sensors14VehicleAirData3RunEv)
*(.text.perf_count)
*(.text._ZN3Ekf16controlMagFusionERKN9estimator9imuSampleE)
*(.text.pthread_sem_give)
*(.text._ZN7sensors10VehicleIMU16ParametersUpdateEb)
*(.text._ZN4uORB20SubscriptionInterval4copyEPv)
*(.text._ZN12I2CSPIDriverI9ICM42688PE3RunEv)
*(.text.imxrt_epcomplete.constprop.0)
*(.text._ZNK6matrix6MatrixIfLj3ELj1EEmiERKS1_)
*(.text._ZN9Commander30handleModeIntentionAndFailsafeEv)
*(.text.perf_event_count)
*(.text._ZN4EKF215PublishAttitudeERKy)
*(.text._ZN19MavlinkStreamRawRpm8get_sizeEv)
*(.text._ZNK3px46atomicIbE4loadEv)
*(.text._ZN29MavlinkStreamHygrometerSensor8get_sizeEv)
*(.text.pthread_mutex_add)
*(.text._ZN12HomePosition6updateEbb)
*(.text._ZN5PX4IO3RunEv)
*(.text.poll_fdsetup)
*(.text._ZN15PositionControl20_accelerationControlEv)
*(.text._ZN3Ekf19controlHeightFusionERKN9estimator9imuSampleE)
*(.text._ZN9Commander19control_status_ledsEbh)
*(.text._ZN6device3I2C8transferEPKhjPhj)
*(.text.orb_publish)
*(.text._ZN7sensors19VehicleAcceleration16ParametersUpdateEb)
*(.text._ZN22MavlinkStreamVibration8get_sizeEv)
*(.text._ZN15MavlinkReceiver15CheckHeartbeatsERKyb)
*(.text._ZNK6matrix7Vector3IfEmiES1_)
*(.text.__aeabi_f2ulz)
*(.text._ZN9ICM42688P26DataReadyInterruptCallbackEiPvS0_)
*(.text._ZN13land_detector23MulticopterLandDetector23_get_maybe_landed_stateEv)
*(.text.acosf)
*(.text._ZN14ImuDownSampler5resetEv)
*(.text._ZN3Ekf31checkVerticalAccelerationHealthERKN9estimator9imuSampleE)
*(.text._ZN6matrix6MatrixIfLj3ELj1EEC1ERKS1_)
*(.text.udp_pollsetup)
*(.text._ZL14timer_callbackPv)
*(.text._ZN3Ekf4fuseERKN6matrix6VectorIfLj24EEEf)
*(.text._ZN13land_detector23MulticopterLandDetector22_set_hysteresis_factorEi)
*(.text.nxsem_wait_irq)
*(.text._ZN20MavlinkCommandSender4lockEv)
*(.text.MEM_LongCopyEnd)
*(.text._ZThn24_N16ControlAllocator3RunEv)
*(.text._ZN15TimestampedListIN20MavlinkCommandSender14command_item_sELi3EE8get_nextEv)
*(.text._ZNK3Ekf21get_ekf_lpos_accuracyEPfS0_)
*(.text._ZN17FlightModeManager17start_flight_taskEv)
*(.text.MEM_DataCopyBytes)
*(.text._ZN29MavlinkStreamLocalPositionNED8get_sizeEv)
*(.text._ZN6SticksC1EP12ModuleParams)
*(.text._ZN27MavlinkStreamServoOutputRawILi1EE4sendEv)
*(.text._ZN3Ekf35updateHorizontalDeadReckoningstatusEv)
*(.text._ZN3Ekf20controlAirDataFusionERKN9estimator9imuSampleE)
*(.text._ZN24FlightTaskManualAltitudeC1Ev)
*(.text._ZN25MavlinkStreamHomePosition4sendEv)
*(.text._ZN24MavlinkParametersManager8send_oneEv)
*(.text._ZN15OutputPredictor29applyCorrectionToOutputBufferERKN6matrix7Vector3IfEES4_)
*(.text._ZN21HealthAndArmingChecks6updateEbb)
*(.text._ZThn24_N22MulticopterRateControl3RunEv)
*(.text._ZN26MavlinkStreamManualControl4sendEv)
*(.text._ZN27MavlinkStreamOpticalFlowRad4sendEv)
*(.text._ZN18mag_bias_estimator16MagBiasEstimator3RunEv)
*(.text._ZN4uORB7Manager11orb_publishEPK12orb_metadataPvPKv)
*(.text._ZN24MavlinkParametersManager18send_untransmittedEv)
*(.text._ZN10MavlinkFTP4sendEv)
*(.text._ZN15ArchPX4IOSerial13_do_interruptEv)
*(.text._ZN3Ekf27controlExternalVisionFusionERKN9estimator9imuSampleE)
*(.text.clock_gettime)
*(.text._ZN3ADC17update_adc_reportEy)
*(.text._ZN32MavlinkStreamGimbalManagerStatus4sendEv)
*(.text._ZN9LockGuardD1Ev) /* itcm-check-ignore */
*(.text._ZN4EKF213PublishStatesERKy)
*(.text._ZN3ADC3RunEv)
*(.text._ZN6BMP38815compensate_dataEhPK16bmp3_uncomp_dataP9bmp3_data)
*(.text._ZN3Ekf20controlFakePosFusionEv)
*(.text._ZN11calibration9Gyroscope13set_device_idEm)
*(.text._ZN24MavlinkStreamOrbitStatus8get_sizeEv)
*(.text.imxrt_progressep)
*(.text.imxrt_gpio_configinput)
*(.text._ZN17FlightModeManager26generateTrajectorySetpointEfRK24vehicle_local_position_s)
*(.text._ZN7Sensors14diff_pres_pollEv)
*(.text._ZN21MavlinkStreamAttitude4sendEv)
*(.text._ZN4EKF220UpdateMagCalibrationERKy)
*(.text._ZN22MavlinkStreamEfiStatus8get_sizeEv)
*(.text._ZN9ICM42688P9DataReadyEv)
*(.text._ZN21MavlinkMissionManager20check_active_missionEv)
*(.text._ZNK3Ekf20get_ekf_vel_accuracyEPfS0_)
*(.text._ZN4EKF216UpdateBaroSampleER17ekf2_timestamps_s)
*(.text._ZN4EKF223UpdateSystemFlagsSampleER17ekf2_timestamps_s)
*(.text._ZThn16_N7sensors22VehicleAngularVelocity3RunEv)
*(.text._ZN29MavlinkStreamObstacleDistance4sendEv)
*(.text._ZN24MavlinkStreamOrbitStatus4sendEv)
*(.text._ZN9Navigator3runEv)
*(.text._ZN24MavlinkParametersManager11send_paramsEv)
*(.text._ZN17MavlinkLogHandler4sendEv)
*(.text._ZN7control10SuperBlock5setDtEf)
*(.text._ZN29MavlinkStreamMountOrientation8get_sizeEv)
*(.text._ZN5PX4IO13io_get_statusEv)
*(.text._ZN26MulticopterAttitudeControl3RunEv)
*(.text._ZThn16_N31ActuatorEffectivenessMultirotor22getEffectivenessMatrixERN21ActuatorEffectiveness13ConfigurationE25EffectivenessUpdateReason)
*(.text._ZN4EKF218PublishStatusFlagsERKy)
*(.text._ZN11WeatherVaneC1EP12ModuleParams)
*(.text._ZN15FailureDetector6updateERK16vehicle_status_sRK22vehicle_control_mode_s)
*(.text._ZN7Mavlink10send_startEi)
*(.text.imxrt_lpspi_setbits)
*(.text._ZN15OutputPredictor37applyCorrectionToVerticalOutputBufferEff)
*(.text._ZN4EKF222UpdateAccelCalibrationERKy)
*(.text._ZN7sensors19VehicleMagnetometer3RunEv)
*(.text._ZN29MavlinkStreamMountOrientation4sendEv)
*(.text._ZN13land_detector12LandDetector19UpdateVehicleAtRestEv)
*(.text._ZN10FlightTask29_evaluateVehicleLocalPositionEv)
*(.text.board_autoled_off)
*(.text.__aeabi_f2lz)
*(.text._ZN32MavlinkStreamCameraImageCaptured4sendEv)
*(.text._ZN21MavlinkStreamOdometry8get_sizeEv)
*(.text._ZN28MavlinkStreamNamedValueFloat4sendEv)
*(.text.__aeabi_ul2f)
*(.text.poll)
*(.text._ZN14FlightTaskAutoD1Ev)
*(.text._ZN4uORB10DeviceNode22get_initial_generationEv)
*(.text._ZN3Ekf23controlGnssHeightFusionERKN9estimator10gnssSampleE)
*(.text._ZN3Ekf40updateOnGroundMotionForOpticalFlowChecksEv)
*(.text._ZN6matrix6MatrixIfLj3ELj1EEC1Ev)
*(.text._ZN14ZeroGyroUpdate6updateER3EkfRKN9estimator9imuSampleE)
*(.text._ZN30MavlinkStreamOpenDroneIdSystem4sendEv)
*(.text._ZN22MavlinkStreamScaledIMU4sendEv)
*(.text.imxrt_ioctl)
*(.text._ZN36MavlinkStreamGimbalDeviceSetAttitude4sendEv)
*(.text._ZN4math13expo_deadzoneIfEEKT_RS2_S3_S3_.isra.0)
*(.text._ZN19StickAccelerationXYC1EP12ModuleParams)
*(.text.imxrt_epsubmit)
*(.text._ZN15PositionControl6updateEf)
*(.text._ZN23MavlinkStreamScaledIMU24sendEv)
*(.text._ZN5PX4IO10io_reg_getEhhPtj)
*(.text.imxrt_dma_send)
*(.text._ZN20MavlinkStreamWindCov4sendEv)
*(.text._ZN7sensors18VotedSensorsUpdate13checkFailoverERNS0_10SensorDataEPKcN6events3px45enums13sensor_type_tE)
*(.text._ZN21MavlinkStreamOdometry4sendEv)
*(.text.vsprintf_internal.constprop.0)
*(.text.udp_pollteardown)
*(.text._ZN12MixingOutput6updateEv)
*(.text.clock_abstime2ticks)
*(.text._ZN13BatteryStatus3RunEv)
*(.text._ZN32MavlinkStreamGimbalManagerStatus8get_sizeEv)
*(.text._ZN10FlightTask15_resetSetpointsEv)
*(.text._ZN9systemlib10Hysteresis20set_state_and_updateEbRKy)
*(.text.devif_callback_free.part.0)
*(.text._ZN6Sticks25checkAndUpdateStickInputsEv)
*(.text.atan2f)
*(.text._ZN23MavlinkStreamRCChannels4sendEv)
*(.text._ZN4EKF221UpdateExtVisionSampleER17ekf2_timestamps_s)
*(.text.imxrt_dmach_stop)
*(.text._ZN9Commander16handleAutoDisarmEv)
*(.text._ZN9Commander11updateTunesEv)
*(.text._ZN4EKF215UpdateMagSampleER17ekf2_timestamps_s)
*(.text._ZN18DataValidatorGroup3putEjyPKfmh)
*(.text._ZNK3Ekf19get_ekf_ctrl_limitsEPfS0_S0_S0_S0_)
*(.text._ZN12FailsafeBase13checkFailsafeEibbRKNS_13ActionOptionsE)
*(.text._ZN17FlightTaskDescendD1Ev)
*(.text._ZN30MavlinkStreamOpenDroneIdSystem8get_sizeEv)
*(.text._ZNK3px46logger9LogWriter10is_startedENS0_7LogTypeE)
*(.text._ZN24FlightTaskManualAltitudeD1Ev)
*(.text._Z35px4_indicate_external_reset_lockout16LockoutComponentb)
*(.text.uart_pollnotify)
*(.text._ZN4EKF215PublishBaroBiasERKy)
*(.text._ZN4EKF221UpdateGyroCalibrationERKy)
*(.text._ZN6matrix9constrainIfLj3ELj1EEENS_6MatrixIT_XT0_EXT1_EEERKS3_S2_S2_)
*(.text._ZN4uORB22SubscriptionMultiArrayI16battery_status_sLh4EE16advertised_countEv)
*(.text._ZN23MavlinkStreamScaledIMU34sendEv)
*(.text.__aeabi_ldivmod)
*(.text._ZN15PositionControl16setInputSetpointERK21trajectory_setpoint_s)
*(.text.nxsig_pendingset)
*(.text.psock_poll)
*(.text._ZN15FailureInjector6updateEv)
*(.text.imxrt_writedtd)
*(.text.cdcacm_wrcomplete)
*(.text.cdcuart_txint)
*(.text._ZN3Ekf33updateVerticalDeadReckoningStatusEv)
*(.text._ZN33FlightTaskManualAltitudeSmoothVelC1Ev)
*(.text.powf)
*(.text._ZN4EKF217PublishEventFlagsERKy)
*(.text._ZN17FlightTaskDescend6updateEv)
*(.text.imxrt_iomux_configure)
*(.text.hrt_store_absolute_time)
*(.text.nxsem_set_protocol)
*(.text.write)
*(.text._ZN22MavlinkStreamSysStatus4sendEv)
*(.text._ZN4EKF216UpdateFlowSampleER17ekf2_timestamps_s)
*(.text._ZN4cdevL10cdev_ioctlEP4fileim)
*(.text._ZN7Mavlink10send_bytesEPKhj)
*(.text._ZNK8Failsafe17checkModeFallbackERK16failsafe_flags_sh)
*(.text.clock_systime_timespec)
*(.text._ZN4uORB10DeviceNode26remove_internal_subscriberEv)
*(.text._ZThn16_N4EKF23RunEv)
*(.text._ZNK3Ekf22computeYawInnovVarAndHEfRfRN6matrix6VectorIfLj24EEE)
*(.text._ZN12ActuatorTest6updateEif)
*(.text._ZN17VelocitySmoothingC1Efff)
*(.text._ZN13AnalogBattery19get_voltage_channelEv)
*(.text._ZN10FlightTask37_evaluateVehicleLocalPositionSetpointEv)
*(.text._ZN4uORB12Subscription11unsubscribeEv)
*(.text.net_lock)
*(.text.clock_time2ticks)
*(.text._ZN12FailsafeBase16updateStartDelayERKyb)
*(.text._ZN23MavlinkStreamStatustext8get_sizeEv)
*(.text._ZN11calibration13Accelerometer13set_device_idEm)
*(.text._ZN3px46logger6Logger18start_stop_loggingEv)
*(.text._ZN14FlightTaskAuto17_evaluateTripletsEv)
*(.text._ZN11calibration9Gyroscope23SensorCorrectionsUpdateEb)
*(.text._ZN25MavlinkStreamMagCalReport4sendEv)
*(.text.imxrt_config_gpio)
*(.text.nxsig_timeout)
*(.text._ZN11RateControl19setSaturationStatusERKN6matrix7Vector3IbEES4_)
*(.text._ZN3Ekf17measurementUpdateERN6matrix6VectorIfLj24EEERKS2_ff)
*(.text.dq_addlast)
*(.text._ZN19MavlinkStreamVFRHUD4sendEv)
*(.text.hrt_call_reschedule)
*(.text.nxsem_boost_priority)
*(.text._ZN4EKF215UpdateGpsSampleER17ekf2_timestamps_s)
*(.text._ZN8StickYawC1EP12ModuleParams)
*(.text._ZN7control12BlockLowPass6updateEf)
*(.text._ZN15FailureDetector26updateImbalancedPropStatusEv)
*(.text._ZN9systemlib10Hysteresis6updateERKy)
*(.text.nxsem_tickwait_uninterruptible)
*(.text._ZN12HomePosition31hasMovedFromCurrentHomeLocationEv)
*(.text.devif_callback_alloc)
*(.text._ZN28MavlinkStreamNamedValueFloat8get_sizeEv)
*(.text._ZN24MavlinkStreamADSBVehicle8get_sizeEv)
*(.text._ZN26MavlinkStreamBatteryStatus8get_sizeEv)
*(.text._ZN26MulticopterPositionControl17parameters_updateEb)
*(.text._ZN3px46logger6Logger29handle_vehicle_command_updateEv)
*(.text.imxrt_lpspi_send)
*(.text._ZN4uORB10DeviceNode23add_internal_subscriberEv)
*(.text._ZN6matrix6MatrixIfLj3ELj1EEaSERKS1_)
*(.text._ZNK6matrix6MatrixIfLj3ELj1EE5emultERKS1_)
*(.text.mallinfo_handler)
*(.text._ZN13land_detector23MulticopterLandDetector14_update_topicsEv)
*(.text._ZN24ManualVelocitySmoothingZC1Ev) /* itcm-check-ignore */
*(.text._ZN3ADC6sampleEj)
*(.text._ZNK3Ekf22isTerrainEstimateValidEv)
*(.text._ZN15EstimatorChecks23setModeRequirementFlagsERK7ContextbbbRK24vehicle_local_position_sRK12sensor_gps_sR16failsafe_flags_sR6Report)
*(.text._ZN11ControlMath11addIfNotNanERff)
*(.text._ZN9Commander21checkForMissionUpdateEv)
*(.text._Z8set_tunei)
*(.text._ZNK6matrix7Vector3IfE5crossERKNS_6MatrixIfLj3ELj1EEE)
*(.text._ZN10FlightTask22_checkEkfResetCountersEv)
*(.text._ZNK6matrix6MatrixIfLj3ELj1EE11isAllFiniteEv)
*(.text._ZN14FlightTaskAuto24_evaluateGlobalReferenceEv)
*(.text._ZN6matrix9constrainIfLj2ELj1EEENS_6MatrixIT_XT0_EXT1_EEERKS3_S2_S2_)
*(.text._ZN3px46logger6Logger23handle_file_write_errorEv)
*(.text._ZN10FlightTask16updateInitializeEv)
*(.text._ZN11calibration9Gyroscope10set_offsetERKN6matrix7Vector3IfEE)
*(.text._ZNK6matrix6VectorIfLj3EE4normEv)
*(.text._ZN14FlightTaskAuto16updateInitializeEv)
*(.text.fabsf)
*(.text._ZN27MavlinkStreamAttitudeTarget8get_sizeEv)
*(.text.nxsem_get_value)
*(.text._ZN13AnalogBattery8is_validEv)
*(.text._ZN4uORB16SubscriptionDataI15home_position_sEC1EPK12orb_metadatah)
*(.text._ZN22MavlinkStreamGPSStatus8get_sizeEv)
*(.text.nxsem_destroyholder)
*(.text.psock_udp_cansend)
*(.text.MEM_DataCopy2_2)
*(.text._ZN10FlightTask8activateERK21trajectory_setpoint_s)
*(.text.sock_file_poll)
*(.text._ZN10Ringbuffer9pop_frontEPhj)
*(.text.nx_write)
*(.text._ZN9Commander18manualControlCheckEv)
*(.text._ZN31MavlinkStreamAttitudeQuaternion4sendEv)
*(.text.nxsem_canceled)
*(.text._ZN10FlightTask21getTrajectorySetpointEv)
*(.text.imxrt_dmach_getcount)
*(.text.sem_clockwait)
*(.text.inet_poll)
*(.text._ZN6BMP3887collectEv)
*(.text._ZN15ArchPX4IOSerial19_do_rx_dma_callbackEbi)
*(.text._ZN15ArchPX4IOSerial10_abort_dmaEv)
*(.text._ZNK15PositionControl24getLocalPositionSetpointER33vehicle_local_position_setpoint_s)
*(.text._ZN3Ekf16controlGpsFusionERKN9estimator9imuSampleE)
*(.text._ZN23MavlinkStreamRCChannels8get_sizeEv)
*(.text._ZN20MavlinkStreamESCInfo8get_sizeEv)
*(.text._ZNK6matrix6VectorIfLj2EE4normEv)
*(.text._Z15arm_auth_updateyb)
*(.text._ZN3LED5ioctlEP4fileim)
*(.text._ZNK3px46logger9LogWriter20had_file_write_errorEv)
*(.text._ZN29MavlinkStreamLocalPositionNED4sendEv)
*(.text._ZNK6matrix6VectorIfLj3EE3dotERKNS_6MatrixIfLj3ELj1EEE)
*(.text.imxrt_lpi2c_setclock)
*(.text._ZN6matrix12typeFunction9constrainIfEET_S2_S2_S2_.part.0)
*(.text._ZN13MapProjection13initReferenceEddy)
*(.text._ZN11calibration13Accelerometer23SensorCorrectionsUpdateEb)
*(.text._ZN31MavlinkStreamAttitudeQuaternion8get_sizeEv)
*(.text._ZN30MavlinkStreamGlobalPositionInt4sendEv)
*(.text._ZN6SticksD1Ev)
*(.text._ZN13NavigatorMode3runEb)
*(.text._ZL19param_find_internalPKcb)
*(.text.uart_datasent)
*(.text._ZN4EKF221PublishOpticalFlowVelERKy)
*(.text.nxsem_destroy)
*(.text.file_write)
*(.text._ZN21MavlinkStreamAltitude4sendEv)
*(.text._ZN7sensors14VehicleAirData12UpdateStatusEv)
*(.text.imxrt_padmux_map)
*(.text._ZN6BMP38815get_sensor_dataEhP9bmp3_data)
*(.text._ZN18MavlinkRateLimiter5checkERKy)
*(.text._ZThn24_N26MulticopterAttitudeControl3RunEv)
*(.text._ZN15ArchPX4IOSerial10_interruptEiPvS0_)
*(.text.imxrt_periphclk_configure)
*(.text._ZN4EKF218UpdateAuxVelSampleER17ekf2_timestamps_s)
*(.text._ZN3RTL11on_inactiveEv)
*(.text._ZN12FailsafeBase10modeCanRunERK16failsafe_flags_sh)
*(.text._ZN4EKF216PublishEvPosBiasERKy)
*(.text._ZN21MavlinkStreamAttitude8get_sizeEv)
*(.text._ZThn16_N7sensors19VehicleAcceleration3RunEv)
*(.text._ZN33MavlinkStreamTimeEstimateToTarget4sendEv)
*(.text._ZN6matrix6MatrixIfLj3ELj1EE6setAllEf)
*(.text._ZN12ModuleParamsD1Ev)
*(.text._ZN3Ekf20controlFakeHgtFusionEv)
*(.text.imxrt_reqcomplete)
*(.text._ZNK6matrix7Vector3IfEmlEf)
*(.text._ZN18ZeroVelocityUpdate6updateER3EkfRKN9estimator9imuSampleE)
*(.text._ZN11ControlMath19addIfNotNanVector3fERN6matrix7Vector3IfEERKS2_)
*(.text._ZN11ControlMath16thrustToAttitudeERKN6matrix7Vector3IfEEfR27vehicle_attitude_setpoint_s)
*(.text.cos)
*(.text.net_unlock)
*(.text._ZN7sensors18VotedSensorsUpdate21setRelativeTimestampsER17sensor_combined_s)
*(.text._ZN12FailsafeBase13ActionOptionsC1ENS_6ActionE)
*(.text._ZN24FlightTaskManualAltitude16updateInitializeEv)
*(.text._ZN26MulticopterPositionControl3RunEv)
*(.text._ZN8Failsafe21fromQuadchuteActParamEi)
*(.text._ZN24VariableLengthRingbuffer9pop_frontEPhj)
*(.text._ZN7control15BlockDerivative6updateEf) /* itcm-check-ignore */
*(.text._ZN5PX4IO10io_reg_getEhh)
*(.text._ZN9Commander18safetyButtonUpdateEv)
*(.text._ZN13BatteryChecks14checkAndReportERK7ContextR6Report)
*(.text._ZN18DataValidatorGroup16get_sensor_stateEj)
*(.text.uart_xmitchars_done)
*(.text._ZN4EKF225PublishYawEstimatorStatusERKy)
*(.text.sin)
*(.text._ZN6Safety19safetyButtonHandlerEv)
*(.text._ZN3Ekf19controlAuxVelFusionERKN9estimator9imuSampleE)
*(.text._ZNK6matrix6MatrixIfLj2ELj1EEplERKS1_)
*(.text._ZThn24_N7Sensors3RunEv)
*(.text._ZN6matrix6MatrixIfLj2ELj1EEC1ERKS1_)
*(.text._ZN10FlightTask10reActivateEv)
*(.text._ZN5PX4IO17io_publish_raw_rcEv)
*(.text._ZNK15PositionControl19getAttitudeSetpointER27vehicle_attitude_setpoint_s)
*(.text._ZN4cdev4CDev4pollEP4fileP6pollfdb)
*(.text._ZN9Commander20offboardControlCheckEv)
*(.text._ZN4EKF216PublishGpsStatusERKy)
*(.text._ZN4uORB12SubscriptionaSEOS0_)
*(.text._ZN15TakeoffHandling18updateTakeoffStateEbbbfbRKy)
*(.text._ZN10ModeChecks14checkAndReportERK7ContextR6Report)
*(.text._ZN14FlightTaskAuto24_updateInternalWaypointsEv)
*(.text._ZN8Failsafe17updateArmingStateERKybRK16failsafe_flags_s)
*(.text.imxrt_lpi2c_modifyreg)
*(.text.up_flush_dcache)
*(.text._ZN5PX4IO16io_handle_statusEt)
*(.text._ZN15GyroCalibration3RunEv)
*(.text.mavlink_start_uart_send)
*(.text.MEM_DataCopy2)
*(.text._ZNK9Commander14getPrearmStateEv)
*(.text._ZN15EstimatorChecks14checkAndReportERK7ContextR6Report)
*(.text._ZN28FlightTaskManualAccelerationD1Ev)
*(.text._ZN11RateControl20getRateControlStatusER18rate_ctrl_status_s)
*(.text._ZN4uORB10DeviceNode15poll_notify_oneEP6pollfdm)
*(.text._ZN3GPS21handleInjectDataTopicEv.part.0)
*(.text._ZN9Commander17systemPowerUpdateEv)
*(.text._ZN4EKF221PublishGlobalPositionERKy)
*(.text._ZNK12FailsafeBase17getSelectedActionERKNS_5StateERK16failsafe_flags_sbbRNS_19SelectedActionStateE)
*(.text.imxrt_padctl_address)
*(.text._ZNK6matrix6VectorIfLj2EE4unitEv)
*(.text._ZN19RcCalibrationChecks14checkAndReportERK7ContextR6Report)
*(.text.devif_conn_callback_free)
*(.text._ZN13InnovationLpf6updateEfff.isra.0)
*(.text._ZN9Commander18landDetectorUpdateEv)
*(.text._ZN3Ekf18updateGroundEffectEv)
*(.text.nxsem_init)
*(.text._ZN9Commander16vtolStatusUpdateEv)
*(.text._ZN6matrix6MatrixIfLj4ELj1EEC1EPKf)
*(.text._ZN11ControlMath20setZeroIfNanVector3fERN6matrix7Vector3IfEE)
*(.text._ZThn8_N3ADC3RunEv)
*(.text._ZN11StickTiltXYC1EP12ModuleParams)
*(.text._ZN12SafetyButton3RunEv)
*(.text._ZN6BMP38811set_op_modeEh)
*(.text._ZN3GPS8callbackE15GPSCallbackTypePviS1_)
*(.text._ZN13AnalogBattery19get_current_channelEv)
*(.text._ZN15EstimatorChecks20checkEstimatorStatusERK7ContextR6ReportRK18estimator_status_s8NavModes)
*(.text._ZN12FailsafeBase11updateDelayERKy)
*(.text._ZN10FlightTask25_evaluateDistanceToGroundEv)
*(.text._ZN4EKF218PublishGnssHgtBiasERKy)
*(.text._ZN6matrix6VectorIfLj3EE9normalizeEv)
*(.text._ZThn16_N7sensors10VehicleIMU3RunEv)
*(.text.__kernel_cos)
*(.text._ZN12SafetyButton19CheckPairingRequestEb)
*(.text.imxrt_dma_txavailable)
*(.text.perf_set_elapsed)
*(.text.pthread_sem_take)
*(.text._ZN8StickYawD1Ev)
*(.text._Z15blink_msg_statev)
*(.text._ZN19AccelerometerChecks14checkAndReportERK7ContextR6Report)
*(.text._ZN8Failsafe14fromGfActParamEi)
*(.text._ZN3Ekf17controlBetaFusionERKN9estimator9imuSampleE)
*(.text._ZN36do_not_explicitly_use_this_namespace5ParamIfLN3px46paramsE919EEC1Ev) /* itcm-check-ignore */
*(.text._ZN22MavlinkStreamHeartbeat8get_sizeEv)
*(.text._ZN6matrix6MatrixIfLj3ELj1EEdVEf)
*(.text._ZN17FlightTaskDescendC1Ev)
*(.text._ZN26MavlinkStreamCameraTrigger8get_sizeEv)
*(.text.iob_navail)
*(.text._ZN12FailsafeBase25removeNonActivatedActionsEv)
*(.text._ZN15TakeoffHandling10updateRampEff)
*(.text._Z7led_offi)
*(.text.led_off)
*(.text.udp_wrbuffer_test)
*(.text._ZNK4math17WelfordMeanVectorIfLj3EE8varianceEv)
*(.text._ZN27MavlinkStreamAttitudeTarget4sendEv)
*(.text._ZN12MixingOutput19updateSubscriptionsEb)
*(.text._ZN10FlightTaskD1Ev)
*(.text._ZThn24_N13land_detector12LandDetector3RunEv)
*(.text._ZN18MavlinkStreamDebug8get_sizeEv)
*(.text._ZN12GPSDriverUBX7receiveEj)
*(.text._ZN13BatteryStatus21parameter_update_pollEb)
*(.text._ZN3RTL18updateDatamanCacheEv)
*(.text.__ieee754_sqrtf)
*(.text._ZThn24_N18mag_bias_estimator16MagBiasEstimator3RunEv)
*(.text.__kernel_sin)
*(.text._ZN11MissionBase17parameters_updateEv)
*(.text.nx_start)
*(.text._ZN3Ekf17controlDragFusionERKN9estimator9imuSampleE)
*(.text._ZNK8Failsafe22modifyUserIntendedModeEN12FailsafeBase6ActionES1_h)
*(.text._ZN3px417ScheduledWorkItem19schedule_trampolineEPv)
*(.text.uart_xmitchars_dma)
*(.text._ZN13land_detector23MulticopterLandDetector19_get_freefall_stateEv)
*(.text._ZThn24_N31MulticopterHoverThrustEstimator3RunEv)
*(.text._ZN11MissionBase11on_inactiveEv)
*(.text._ZN21FailureDetectorChecks14checkAndReportERK7ContextR6Report)
*(.text._ZN12SystemChecks14checkAndReportERK7ContextR6Report)
*(.text._ZN6matrix6MatrixIfLj3ELj1EEC1EPKf)
*(.text.imxrt_padmux_address)
*(.text._ZN19MavlinkStreamVFRHUD8get_sizeEv)
*(.text._ZN15EstimatorChecks15checkSensorBiasERK7ContextR6Report8NavModes)
*(.text._ZN20ImuConsistencyChecks14checkAndReportERK7ContextR6Report)
*(.text._ZN28MavlinkStreamGpsGlobalOrigin8get_sizeEv)
*(.text.MEM_DataCopy2_1)
*(.text._ZN6BMP3887measureEv)
*(.text._ZN4EKF217PublishRngHgtBiasERKy)
*(.text._ZN36MavlinkStreamPositionTargetGlobalInt8get_sizeEv)
*(.text._ZN28MavlinkStreamEstimatorStatus8get_sizeEv)
*(.text.up_clean_dcache)
*(.text._ZThn24_N26MulticopterPositionControl3RunEv)
*(.text._ZN16FlightTimeChecks14checkAndReportERK7ContextR6Report)
*(.text._ZN13ManualControl12processInputEy)
*(.text._ZN17CpuResourceChecks14checkAndReportERK7ContextR6Report)
*(.text._ZN10GyroChecks14checkAndReportERK7ContextR6Report)
*(.text._ZN8Failsafe26fromImbalancedPropActParamEi)
*(.text._ZThn24_N13BatteryStatus3RunEv)
*(.text.mm_foreach)
*(.text._ZN35MavlinkStreamPositionTargetLocalNed8get_sizeEv)
*(.text._ZN32MavlinkStreamNavControllerOutput8get_sizeEv)
*(.text._ZN6matrix8wrap_2piIfEET_S1_)
*(.text._ZN4uORB7Manager30orb_remove_internal_subscriberEPv)
*(.text._ZN10BMP388_I2C7get_regEhPh)
*(.text._ZN4math17WelfordMeanVectorIfLj3EE5resetEv)
*(.text._ZN27MavlinkStreamScaledPressure8get_sizeEv)
*(.text._ZN3RTL17parameters_updateEv)
*(.text._ZN18EstimatorInterface11setBaroDataERKN9estimator10baroSampleE.part.0)
*(.text._ZN32MavlinkStreamOpenDroneIdLocation8get_sizeEv)
*(.text._ZN21MavlinkStreamTimesync4sendEv)
*(.text._ZN9Navigator23reset_position_setpointER19position_setpoint_s)
*(.text._ZN19RcAndDataLinkChecks14checkAndReportERK7ContextR6Report)
*(.text.imxrt_dma_txcallback)
*(.text._ZN24MavlinkParametersManager11send_uavcanEv)
*(.text._ZN4uORB10DeviceNode4readEP4filePcj)
*(.text._ZN4uORB10DeviceNode10poll_stateEP4file)
*(.text._ZN4uORB7Manager8orb_copyEPK12orb_metadataiPv)
*(.text._ZN27MavlinkStreamServoOutputRawILi0EE8get_sizeEv)
*(.text._ZN8Geofence3runEv)
*(.text._ZN15EstimatorChecks25checkEstimatorStatusFlagsERK7ContextR6ReportRK18estimator_status_sRK24vehicle_local_position_s)
*(.text._ZN18MagnetometerChecks14checkAndReportERK7ContextR6Report)
*(.text._ZN6events9SendEvent3RunEv)
*(.text._ZN30MavlinkStreamGlobalPositionInt8get_sizeEv)
*(.text._ZN22MavlinkStreamESCStatus8get_sizeEv)
*(.text._Z20px4_spi_bus_externalRK13px4_spi_bus_t)
*(.text.read)
*(.text._ZN4uORB15PublicationBaseD1Ev)
*(.text._ZN22MavlinkStreamDebugVect8get_sizeEv)
*(.text._ZN7Mission11on_inactiveEv)
*(.text._ZN7sensors19VehicleMagnetometer20UpdateMagCalibrationEv)
*(.text._ZN11calibration27FindCurrentCalibrationIndexEPKcm)
*(.text._ZN4cdevL9cdev_readEP4filePcj)
*(.text.sem_timedwait)
*(.text.snprintf)
*(.text._ZN27MavlinkStreamOpticalFlowRad8get_sizeEv)
*(.text._ZNK6matrix6MatrixIfLj3ELj1EE6copyToEPf)
*(.text._ZN6Report13healthFailureIJhEEEv8NavModes20HealthComponentIndexmRKN6events9LogLevelsEPKcDpT_.isra.0.constprop.0)
*(.text._ZN13BatteryChecks16rtlEstimateCheckERK7ContextR6Reportf)
*(.text.sigemptyset)
*(.text.nx_read)
